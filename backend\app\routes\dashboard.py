from flask import Blueprint, jsonify
from app import db
from app.models import Server, Migration, User, AuditLog
from app.utils.decorators import jwt_required, require_permission, handle_errors
from sqlalchemy import func, and_, text
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/stats', methods=['GET'])
@jwt_required()
@require_permission('view_migrations')
@handle_errors
def get_dashboard_stats():
    """Get dashboard statistics"""
    
    # Server statistics
    total_servers = Server.query.filter_by(is_active=True).count()
    online_servers = Server.query.filter(
        and_(Server.is_active == True, Server.status == 'online')
    ).count()
    
    # Migration statistics
    total_migrations = Migration.query.count()
    active_migrations = Migration.query.filter(
        Migration.status.in_(['pending', 'running', 'paused'])
    ).count()
    completed_migrations = Migration.query.filter_by(status='completed').count()
    failed_migrations = Migration.query.filter_by(status='failed').count()
    
    # Recent migrations (last 10)
    recent_migrations = Migration.query.order_by(
        Migration.created_at.desc()
    ).limit(10).all()
    
    # Server status distribution
    server_status_query = db.session.query(
        Server.status,
        func.count(Server.id).label('count')
    ).filter_by(is_active=True).group_by(Server.status).all()
    
    server_status_distribution = {
        status: count for status, count in server_status_query
    }
    
    # Migration status distribution
    migration_status_query = db.session.query(
        Migration.status,
        func.count(Migration.id).label('count')
    ).group_by(Migration.status).all()
    
    migration_status_distribution = {
        status: count for status, count in migration_status_query
    }
    
    # Migration trends (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    migration_trends = db.session.query(
        func.date(Migration.created_at).label('date'),
        func.count(Migration.id).label('count')
    ).filter(
        Migration.created_at >= thirty_days_ago
    ).group_by(
        func.date(Migration.created_at)
    ).order_by('date').all()
    
    # Success rate (last 30 days)
    recent_completed = Migration.query.filter(
        and_(
            Migration.created_at >= thirty_days_ago,
            Migration.status == 'completed'
        )
    ).count()
    
    recent_failed = Migration.query.filter(
        and_(
            Migration.created_at >= thirty_days_ago,
            Migration.status == 'failed'
        )
    ).count()
    
    total_finished = recent_completed + recent_failed
    success_rate = (recent_completed / total_finished * 100) if total_finished > 0 else 0
    
    # Average migration duration
    completed_migrations_with_duration = Migration.query.filter(
        and_(
            Migration.status == 'completed',
            Migration.started_at.isnot(None),
            Migration.completed_at.isnot(None)
        )
    ).all()
    
    if completed_migrations_with_duration:
        total_duration = sum(
            (m.completed_at - m.started_at).total_seconds()
            for m in completed_migrations_with_duration
        )
        avg_duration = total_duration / len(completed_migrations_with_duration)
    else:
        avg_duration = 0
    
    return jsonify({
        'servers': {
            'total': total_servers,
            'online': online_servers,
            'offline': total_servers - online_servers,
            'status_distribution': server_status_distribution
        },
        'migrations': {
            'total': total_migrations,
            'active': active_migrations,
            'completed': completed_migrations,
            'failed': failed_migrations,
            'success_rate': round(success_rate, 1),
            'avg_duration': round(avg_duration, 0),
            'status_distribution': migration_status_distribution,
            'trends': [
                {
                    'date': date.strftime('%Y-%m-%d'),
                    'count': count
                }
                for date, count in migration_trends
            ]
        },
        'recent_migrations': [
            migration.to_dict() for migration in recent_migrations
        ]
    }), 200

@dashboard_bp.route('/activity', methods=['GET'])
@jwt_required()
@require_permission('view_audit_logs')
@handle_errors
def get_recent_activity():
    """Get recent system activity"""
    
    # Recent audit logs (last 50)
    recent_logs = AuditLog.query.order_by(
        AuditLog.created_at.desc()
    ).limit(50).all()
    
    # Activity by action type (last 7 days)
    seven_days_ago = datetime.utcnow() - timedelta(days=7)
    activity_stats = db.session.query(
        AuditLog.action,
        func.count(AuditLog.id).label('count')
    ).filter(
        AuditLog.created_at >= seven_days_ago
    ).group_by(AuditLog.action).all()
    
    # Most active users (last 7 days)
    active_users = db.session.query(
        User.username,
        User.first_name,
        User.last_name,
        func.count(AuditLog.id).label('activity_count')
    ).join(
        AuditLog, User.id == AuditLog.user_id
    ).filter(
        AuditLog.created_at >= seven_days_ago
    ).group_by(
        User.id, User.username, User.first_name, User.last_name
    ).order_by(
        func.count(AuditLog.id).desc()
    ).limit(10).all()
    
    return jsonify({
        'recent_logs': [log.to_dict() for log in recent_logs],
        'activity_stats': [
            {'action': action, 'count': count}
            for action, count in activity_stats
        ],
        'active_users': [
            {
                'username': username,
                'full_name': f"{first_name} {last_name}",
                'activity_count': count
            }
            for username, first_name, last_name, count in active_users
        ]
    }), 200

@dashboard_bp.route('/system-health', methods=['GET'])
@jwt_required()
@require_permission('view_migrations')
@handle_errors
def get_system_health():
    """Get system health metrics"""
    
    # Database connection test
    try:
        db.session.execute(text('SELECT 1'))
        db_status = 'healthy'
        db_message = 'Database connection successful'
    except Exception as e:
        db_status = 'error'
        db_message = f'Database connection failed: {str(e)}'
    
    # Redis connection test
    try:
        from app import redis_client
        redis_client.ping()
        redis_status = 'healthy'
        redis_message = 'Redis connection successful'
    except Exception as e:
        redis_status = 'error'
        redis_message = f'Redis connection failed: {str(e)}'
    
    # Check for stuck migrations
    stuck_migrations = Migration.query.filter(
        and_(
            Migration.status == 'running',
            Migration.started_at < datetime.utcnow() - timedelta(hours=24)
        )
    ).count()
    
    # Check disk space (if possible)
    import shutil
    try:
        disk_usage = shutil.disk_usage('/')
        disk_free_gb = disk_usage.free / (1024**3)
        disk_total_gb = disk_usage.total / (1024**3)
        disk_used_percent = ((disk_usage.total - disk_usage.free) / disk_usage.total) * 100
        
        if disk_used_percent > 90:
            disk_status = 'warning'
            disk_message = f'Disk usage high: {disk_used_percent:.1f}%'
        elif disk_used_percent > 95:
            disk_status = 'error'
            disk_message = f'Disk usage critical: {disk_used_percent:.1f}%'
        else:
            disk_status = 'healthy'
            disk_message = f'Disk usage normal: {disk_used_percent:.1f}%'
    except Exception:
        disk_status = 'unknown'
        disk_message = 'Unable to check disk usage'
        disk_free_gb = 0
        disk_total_gb = 0
        disk_used_percent = 0
    
    # Overall system status
    if db_status == 'error' or redis_status == 'error':
        overall_status = 'error'
    elif stuck_migrations > 0 or disk_status == 'error':
        overall_status = 'warning'
    else:
        overall_status = 'healthy'
    
    return jsonify({
        'overall_status': overall_status,
        'components': {
            'database': {
                'status': db_status,
                'message': db_message
            },
            'redis': {
                'status': redis_status,
                'message': redis_message
            },
            'disk': {
                'status': disk_status,
                'message': disk_message,
                'free_gb': round(disk_free_gb, 2),
                'total_gb': round(disk_total_gb, 2),
                'used_percent': round(disk_used_percent, 1)
            }
        },
        'alerts': {
            'stuck_migrations': stuck_migrations
        }
    }), 200

@dashboard_bp.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint (no auth required)"""
    try:
        # Basic database check
        db.session.execute(text('SELECT 1'))
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503
