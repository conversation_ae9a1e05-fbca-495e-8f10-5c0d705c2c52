version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: migration_postgres
    environment:
      POSTGRES_DB: migration_service
      POSTGRES_USER: migration_user
      POSTGRES_PASSWORD: migration_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - migration_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: migration_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - migration_network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: migration_backend
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=************************************************************/migration_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=your-secret-key-change-in-production
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./backend/configs:/app/configs
      - ./backend/scripts:/app/scripts
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker operations
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - migration_network
    restart: unless-stopped

  # Celery Worker
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: migration_celery
    command: celery -A app.celery worker --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=************************************************************/migration_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=your-secret-key-change-in-production
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./backend/configs:/app/configs
      - ./backend/scripts:/app/scripts
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
    networks:
      - migration_network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: migration_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - migration_network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: migration_nginx
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - migration_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  migration_network:
    driver: bridge

