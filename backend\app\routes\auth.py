from flask import Blueprint, request, jsonify, current_app
from datetime import datetime
from app import db
from app.models import User, Role

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    """Simple user registration"""
    try:
        data = request.get_json()

        # Create new user without validation
        user = User(
            username=data.get('username', 'user'),
            email=data.get('email', '<EMAIL>'),
            password=data.get('password', 'password'),
            first_name=data.get('first_name', 'User'),
            last_name=data.get('last_name', 'Name')
        )

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'message': 'User registered successfully',
            'user': user.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Registration error: {str(e)}")
        return jsonify({'error': 'Registration failed'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """Simple direct login"""
    try:
        data = request.get_json()

        # Find user by username or create if not exists
        username = data.get('username', 'user')
        user = User.query.filter_by(username=username).first()

        if not user:
            # Create user if doesn't exist
            user = User(
                username=username,
                email=f"{username}@example.com",
                password='password',
                first_name=username,
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()

        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Login error: {str(e)}")
        return jsonify({'error': 'Login failed'}), 500

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """Simple logout"""
    return jsonify({'message': 'Successfully logged out'}), 200

@auth_bp.route('/me', methods=['GET'])
def get_current_user():
    """Get user information"""
    try:
        # Return first user or create default user
        user = User.query.first()
        if not user:
            user = User(
                username='default_user',
                email='<EMAIL>',
                password='password',
                first_name='Default',
                last_name='User'
            )
            db.session.add(user)
            db.session.commit()

        return jsonify({
            'user': user.to_dict()
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get current user error: {str(e)}")
        return jsonify({'error': 'Failed to get user information'}), 500
