from flask import Blueprint, request, jsonify, current_app
from app import db
from app.models import User, Role, AuditLog
from app.utils.decorators import jwt_required, get_jwt_identity

users_bp = Blueprint('users', __name__)

@users_bp.route('', methods=['GET'])
def get_users():
    """Get list of users with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    
    query = User.query
    
    # Apply search filter
    if search:
        query = query.filter(
            (User.username.ilike(f'%{search}%')) |
            (User.email.ilike(f'%{search}%')) |
            (User.first_name.ilike(f'%{search}%')) |
            (User.last_name.ilike(f'%{search}%'))
        )
    
    # Apply role filter
    if role_filter:
        query = query.join(User.roles).filter(Role.name == role_filter)
    
    # Apply status filter
    if status_filter == 'active':
        query = query.filter(User.is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(User.is_active == False)
    elif status_filter == 'verified':
        query = query.filter(User.is_verified == True)
    elif status_filter == 'unverified':
        query = query.filter(User.is_verified == False)
    
    # Order by creation date
    query = query.order_by(User.created_at.desc())
    
    # Paginate
    pagination = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    users = pagination.items
    
    return jsonify({
        'users': [user.to_dict() for user in users],
        'pagination': {
            'page': page,
            'pages': pagination.pages,
            'per_page': per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    }), 200

@users_bp.route('/<user_id>', methods=['GET'])
def get_user(user_id):
    """Get specific user details"""
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    return jsonify({
        'user': user.to_dict()
    }), 200

@users_bp.route('', methods=['POST'])
def create_user():
    """Create new user"""
    try:
        data = request.get_json()

        # Create user without validation
        user = User(
            username=data.get('username', 'user'),
            email=data.get('email', '<EMAIL>'),
            password=data.get('password', 'password'),
            first_name=data.get('first_name', 'User'),
            last_name=data.get('last_name', 'Name')
        )

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'message': 'User created successfully',
            'user': user.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Create user error: {str(e)}")
        return jsonify({'error': 'User creation failed'}), 500

@users_bp.route('/<user_id>', methods=['PUT'])
def update_user(user_id):
    """Update user information"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()

        # Update fields if provided
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'email' in data:
            user.email = data['email']
        if 'username' in data:
            user.username = data['username']

        db.session.commit()

        return jsonify({
            'message': 'User updated successfully',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Update user error: {str(e)}")
        return jsonify({'error': 'User update failed'}), 500

@users_bp.route('/<user_id>', methods=['DELETE'])
def delete_user(user_id):
    """Delete user"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Soft delete - just deactivate the user
        user.is_active = False
        db.session.commit()

        return jsonify({'message': 'User deactivated successfully'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete user error: {str(e)}")
        return jsonify({'error': 'User deletion failed'}), 500

@users_bp.route('/roles', methods=['GET'])
def get_roles():
    """Get list of available roles"""
    try:
        roles = Role.query.order_by(Role.name).all()

        return jsonify({
            'roles': [role.to_dict() for role in roles]
        }), 200

    except Exception as e:
        current_app.logger.error(f"Get roles error: {str(e)}")
        return jsonify({'error': 'Failed to get roles'}), 500

@users_bp.route('/roles', methods=['POST'])
def create_role():
    """Create new role"""
    try:
        data = request.get_json()

        role = Role(
            name=data.get('name', 'user'),
            description=data.get('description', ''),
            permissions=data.get('permissions', [])
        )

        db.session.add(role)
        db.session.commit()

        return jsonify({
            'message': 'Role created successfully',
            'role': role.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Create role error: {str(e)}")
        return jsonify({'error': 'Role creation failed'}), 500
