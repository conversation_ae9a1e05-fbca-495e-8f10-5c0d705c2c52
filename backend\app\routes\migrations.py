from flask import Blueprint, request, jsonify, current_app, send_file
from app import db, socketio
from app.models import Migration, MigrationTemplate, Server, User, MigrationLog, AuditLog
from app.services.migration_service import MigrationService
from app.utils.decorators import jwt_required, get_jwt_identity, require_permission, audit_log, handle_errors, validate_json
import os

migrations_bp = Blueprint('migrations', __name__)

@migrations_bp.route('', methods=['GET'])
def get_migrations():
    """Get list of migrations with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 20, type=int), 100)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    source_server = request.args.get('source_server', '')
    target_server = request.args.get('target_server', '')
    
    query = Migration.query
    
    # Apply search filter
    if search:
        query = query.filter(
            (Migration.name.ilike(f'%{search}%')) |
            (Migration.description.ilike(f'%{search}%'))
        )
    
    # Apply status filter
    if status_filter:
        query = query.filter(Migration.status == status_filter)
    
    # Apply server filters
    if source_server:
        query = query.filter(Migration.source_server_id == source_server)
    
    if target_server:
        query = query.filter(Migration.target_server_id == target_server)
    
    # Order by creation date (newest first)
    query = query.order_by(Migration.created_at.desc())
    
    # Paginate
    pagination = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    migrations = pagination.items
    
    return jsonify({
        'migrations': [migration.to_dict() for migration in migrations],
        'pagination': {
            'page': page,
            'pages': pagination.pages,
            'per_page': per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    }), 200

@migrations_bp.route('/<migration_id>', methods=['GET'])
def get_migration(migration_id):
    """Get specific migration details"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404

    return jsonify({
        'migration': migration.to_dict(include_config=True)
    }), 200

@migrations_bp.route('', methods=['POST'])
def create_migration():
    """Create new migration"""
    try:
        data = request.get_json()

        # Create migration without validation
        migration = Migration(
            name=data.get('name', 'Migration'),
            description=data.get('description', ''),
            source_server_id=data.get('source_server_id'),
            target_server_id=data.get('target_server_id'),
            config=data.get('config', {}),
            template_id=data.get('template_id'),
            created_by=1,  # Default user ID
            scheduled_at=data.get('scheduled_at')
        )

        db.session.add(migration)
        db.session.commit()

        return jsonify({
            'message': 'Migration created successfully',
            'migration': migration.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Create migration error: {str(e)}")
        return jsonify({'error': 'Migration creation failed'}), 500

@migrations_bp.route('/<migration_id>', methods=['PUT'])
def update_migration(migration_id):
    """Update migration information"""
    try:
        migration = Migration.query.get(migration_id)
        if not migration:
            return jsonify({'error': 'Migration not found'}), 404

        data = request.get_json()

        # Update fields if provided
        if 'name' in data:
            migration.name = data['name']
        if 'description' in data:
            migration.description = data['description']
        if 'scheduled_at' in data:
            migration.scheduled_at = data['scheduled_at']
        if 'config' in data:
            migration.config = data['config']

        db.session.commit()

        return jsonify({
            'message': 'Migration updated successfully',
            'migration': migration.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Update migration error: {str(e)}")
        return jsonify({'error': 'Migration update failed'}), 500

@migrations_bp.route('/<migration_id>', methods=['DELETE'])
@jwt_required()
@require_permission('delete_migrations')
@audit_log('delete_migration', 'migration')
@handle_errors
def delete_migration(migration_id):
    """Delete migration"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404
    
    # Can only delete pending or completed migrations
    if migration.status in ['running', 'paused']:
        return jsonify({'error': 'Cannot delete running or paused migrations'}), 400
    
    db.session.delete(migration)
    db.session.commit()
    
    return jsonify({'message': 'Migration deleted successfully'}), 200

@migrations_bp.route('/<migration_id>/start', methods=['POST'])
@jwt_required()
@require_permission('manage_migrations')
@audit_log('start_migration', 'migration')
@handle_errors
def start_migration(migration_id):
    """Start migration execution"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404
    
    if migration.status != 'pending':
        return jsonify({'error': 'Migration is not in pending state'}), 400
    
    # Start migration using service
    migration_service = MigrationService()
    try:
        migration_service.start_migration(migration_id)
        return jsonify({'message': 'Migration started successfully'}), 200
    except Exception as e:
        return jsonify({'error': f'Failed to start migration: {str(e)}'}), 500

@migrations_bp.route('/<migration_id>/pause', methods=['POST'])
@jwt_required()
@require_permission('manage_migrations')
@audit_log('pause_migration', 'migration')
@handle_errors
def pause_migration(migration_id):
    """Pause migration execution"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404
    
    if migration.status != 'running':
        return jsonify({'error': 'Migration is not running'}), 400
    
    # Pause migration using service
    migration_service = MigrationService()
    try:
        migration_service.pause_migration(migration_id)
        return jsonify({'message': 'Migration paused successfully'}), 200
    except Exception as e:
        return jsonify({'error': f'Failed to pause migration: {str(e)}'}), 500

@migrations_bp.route('/<migration_id>/cancel', methods=['POST'])
@jwt_required()
@require_permission('manage_migrations')
@audit_log('cancel_migration', 'migration')
@handle_errors
def cancel_migration(migration_id):
    """Cancel migration execution"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404
    
    if not migration.can_be_cancelled():
        return jsonify({'error': 'Migration cannot be cancelled in current state'}), 400
    
    # Cancel migration using service
    migration_service = MigrationService()
    try:
        migration_service.cancel_migration(migration_id)
        return jsonify({'message': 'Migration cancelled successfully'}), 200
    except Exception as e:
        return jsonify({'error': f'Failed to cancel migration: {str(e)}'}), 500

@migrations_bp.route('/<migration_id>/logs', methods=['GET'])
@jwt_required()
@require_permission('view_migrations')
@handle_errors
def get_migration_logs(migration_id):
    """Get migration logs"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404
    
    # Get logs from database
    logs = MigrationLog.query.filter_by(migration_id=migration_id).order_by(MigrationLog.created_at).all()
    
    return jsonify({
        'logs': [log.to_dict() for log in logs]
    }), 200

@migrations_bp.route('/<migration_id>/logs/download', methods=['GET'])
@jwt_required()
@require_permission('view_migrations')
@handle_errors
def download_migration_logs(migration_id):
    """Download migration logs as file"""
    migration = Migration.query.get(migration_id)
    if not migration:
        return jsonify({'error': 'Migration not found'}), 404
    
    # Check if log file exists
    log_file_path = os.path.join(current_app.config['LOG_FOLDER'], f"{migration_id}.log")
    if not os.path.exists(log_file_path):
        return jsonify({'error': 'Log file not found'}), 404
    
    return send_file(
        log_file_path,
        as_attachment=True,
        download_name=f"migration_{migration.name}_{migration_id}.log",
        mimetype='text/plain'
    )

@migrations_bp.route('/templates', methods=['GET'])
@jwt_required()
@require_permission('view_migrations')
@handle_errors
def get_migration_templates():
    """Get list of migration templates"""
    templates = MigrationTemplate.query.filter_by(is_active=True).order_by(MigrationTemplate.name).all()
    
    return jsonify({
        'templates': [template.to_dict() for template in templates]
    }), 200

@migrations_bp.route('/templates', methods=['POST'])
@jwt_required()
@require_permission('manage_migrations')
@validate_json(['name', 'config_template'])
@audit_log('create_migration_template', 'migration_template')
@handle_errors
def create_migration_template():
    """Create new migration template"""
    data = request.get_json()
    
    # Check if template already exists
    if MigrationTemplate.query.filter_by(name=data['name']).first():
        return jsonify({'error': 'Migration template already exists'}), 409
    
    template = MigrationTemplate(
        name=data['name'],
        description=data.get('description'),
        category=data.get('category'),
        config_template=data['config_template'],
        default_values=data.get('default_values', {}),
        required_fields=data.get('required_fields', []),
        version=data.get('version', '1.0'),
        tags=data.get('tags', [])
    )
    
    db.session.add(template)
    db.session.commit()
    
    return jsonify({
        'message': 'Migration template created successfully',
        'template': template.to_dict()
    }), 201

# WebSocket events for real-time updates
@socketio.on('join_migration')
@jwt_required()
def on_join_migration(data):
    """Join migration room for real-time updates"""
    migration_id = data.get('migration_id')
    if migration_id:
        # Verify user has permission to view this migration
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if user and user.has_permission('view_migrations'):
            from flask_socketio import join_room
            join_room(f"migration_{migration_id}")
            socketio.emit('joined', {'migration_id': migration_id})

@socketio.on('leave_migration')
@jwt_required()
def on_leave_migration(data):
    """Leave migration room"""
    migration_id = data.get('migration_id')
    if migration_id:
        from flask_socketio import leave_room
        leave_room(f"migration_{migration_id}")
        socketio.emit('left', {'migration_id': migration_id})
